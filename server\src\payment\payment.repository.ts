import { FieldValue } from 'firebase-admin/firestore';
import { Invoice } from 'src/invoice/invoice.entity';
import { BulkPayment } from './bulk_payment.entity';
import { Provider } from 'src/provider/provider.entity';
import { CreatePaymentData } from './payment.types';
import { InvoiceRepository } from 'src/invoice/invoice.repository';
import { ProviderRepository } from 'src/provider/provider.repository';

import { Injectable, NotFoundException } from '@nestjs/common'; // Import NotFoundException
import { Payment } from './payment.entity';

@Injectable()
export class PaymentRepository {
  constructor(
    private readonly invoiceRepository: InvoiceRepository,
    private readonly providerRepository: ProviderRepository,
  ) {}

  private getPaymentsCollection(
    providerId: Provider['id'],
    invoiceId: Invoice['id'],
  ) {
    return this.invoiceRepository
      .getInvoiceCollection(providerId)
      .doc(invoiceId)
      .collection('payments');
  }

  private getBulkPaymentsCollection(providerId: Provider['id']) {
    return this.providerRepository
      .getProviderCollection()
      .doc(providerId)
      .collection('bulk_payments');
  }

  createPayment(data: CreatePaymentData, provider_id: Provider['id']) {
    return this.getPaymentsCollection(provider_id, data.invoice_id).add({
      ...data,
      credit_balance: data.credit_balance || 0,
      created_at: FieldValue.serverTimestamp(),
      updated_at: FieldValue.serverTimestamp(),
    });
  }

  createBulkPayment(data: Partial<BulkPayment>, providerId: Provider['id']) {
    return this.getBulkPaymentsCollection(providerId).add({
      ...data,
      created_at: FieldValue.serverTimestamp(),
      updated_at: FieldValue.serverTimestamp(),
    });
  }

  async getById(
    providerId: Provider['id'],
    invoiceId: Invoice['id'],
    paymentId: Payment['id'],
  ) {
    const doc = await this.getPaymentsCollection(providerId, invoiceId)
      .doc(paymentId)
      .get();

    if (!doc.exists) {
      // Import NotFoundException from @nestjs/common if not already imported
      throw new NotFoundException(
        `Payment with ID ${paymentId} not found for invoice ${invoiceId} and provider ${providerId}`,
      );
    }

    // Combine ID with data only if document exists
    return { id: doc.id, ...doc.data() } as Payment;
  }

  async getByInvoiceId(invoiceId: Invoice['id'], providerId: Provider['id']) {
    const snapshot = await this.getPaymentsCollection(
      providerId,
      invoiceId,
    ).get();

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Payment[];
  }

  updatePayment(
    providerId: Provider['id'],
    invoiceId: Invoice['id'],
    paymentId: Payment['id'],
    data: Partial<Payment>,
  ) {
    return this.getPaymentsCollection(providerId, invoiceId)
      .doc(paymentId)
      .update({
        ...data,
        updated_at: FieldValue.serverTimestamp(),
      });
  }

  deletePayment(
    providerId: Provider['id'],
    invoiceId: Invoice['id'],
    paymentId: Payment['id'],
  ) {
    return this.getPaymentsCollection(providerId, invoiceId)
      .doc(paymentId)
      .delete();
  }
}
