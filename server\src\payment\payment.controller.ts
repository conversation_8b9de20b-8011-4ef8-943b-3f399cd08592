import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { PaymentService } from './payment.service';
import {
  createBulkPaymentDto,
  createPaymentDto,
  PaymentIdDto,
  ProviderAndInvoiceIdDto,
  updatePaymentDto,
} from './payment.dto';
import { ProviderIdDto } from 'src/provider/provider.dto';
import { InvoiceIdDto } from 'src/invoice/invoice.dto';

@Controller('payment')
@UsePipes(new ValidationPipe({ whitelist: true }))
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @Post('bulk')
  createBulkPayment(@Body() body: createBulkPaymentDto) {
    const { provider_id, ...rest } = body;

    return this.paymentService.createBulkPayment(rest, provider_id);
  }

  @Post(':invoiceId')
  create(
    @Param() params: InvoiceIdDto,
    @Query() querys: ProviderIdDto,
    @Body() body: createPaymentDto,
  ) {
    return this.paymentService.create(
      {
        ...body,
        invoice_id: params.invoiceId,
      },
      querys.providerId,
    );
  }

  @Get(':invoiceId')
  getByInvoiceId(
    @Param() params: InvoiceIdDto,
    @Query()
    querys: ProviderIdDto,
  ) {
    return this.paymentService.getByInvoiceId(
      params.invoiceId,
      querys.providerId,
    );
  }

  @Put(':paymentId')
  update(
    @Param() params: PaymentIdDto,
    @Query()
    querys: ProviderAndInvoiceIdDto,
    @Body()
    body: updatePaymentDto,
  ) {
    return this.paymentService.update(
      querys.providerId,
      params.paymentId,
      querys.invoiceId,
      body,
    );
  }

  @Delete(':paymentId')
  delete(
    @Param() params: PaymentIdDto,
    @Query()
    querys: ProviderAndInvoiceIdDto,
  ) {
    return this.paymentService.delete(
      querys.providerId,
      params.paymentId,
      querys.invoiceId,
    );
  }
}
